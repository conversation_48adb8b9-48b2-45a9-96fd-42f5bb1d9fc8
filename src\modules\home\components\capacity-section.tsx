'use client';

import Link from 'next/link';
import { useEffect, useState, useRef } from 'react';

const cardData = [
  {
    title: 'AIRLINES',
    description: 'Commercial aircraft parts and comprehensive airline support solutions',
    imageUrl: '/images/airline.png',
    href: '/aircraft/airlines',
    gradientFrom: 'from-blue-900',
    gradientTo: 'to-blue-700',
    overlayOpacity: 'bg-black/30',
    hoverOverlayOpacity: 'group-hover:bg-black/20',
  },
  {
    title: 'HELICOPTERS',
    description: 'Rotorcraft parts and specialized helicopter maintenance solutions',
    imageUrl: '/images/helicopter.png',
    href: '/aircraft/helicopters',
    gradientFrom: 'from-yellow-600',
    gradientTo: 'to-orange-600',
    overlayOpacity: 'bg-black/30',
    hoverOverlayOpacity: 'group-hover:bg-black/20',
  },
  {
    title: 'DEFENCE',
    description: 'Military aircraft support with security-cleared processes and documentation',
    imageUrl: '/images/defense.png',
    href: '/aircraft/defence',
    gradientFrom: 'from-green-800',
    gradientTo: 'to-gray-800',
    overlayOpacity: 'bg-black/40',
    hoverOverlayOpacity: 'group-hover:bg-black/30',
  },
];

// Custom hook to detect if element is in viewport
function useInView(ref: React.RefObject<HTMLDivElement>) {
  const [isIntersecting, setIntersecting] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIntersecting(true);
          observer.disconnect(); // Animate once when visible
        }
      },
      {
        threshold: 0.1,
      }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [ref]);

  return isIntersecting;
}

const CapacitySection = () => {
  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-5xl font-extrabold text-gray-900 mb-3 tracking-tight">
            OUR CAPACITY
          </h2>
          <div className="mx-auto w-28 h-1 bg-teal-600 rounded-full shadow-md"></div>
        </div>

        {/* Cards Grid */}
        <div className="grid md:grid-cols-3 gap-10">
          {cardData.map(({ title, description, imageUrl, href, gradientFrom, gradientTo, overlayOpacity, hoverOverlayOpacity }) => {
            const ref = useRef<HTMLDivElement>(null);
            const isVisible = useInView(ref);

            return (
              <div
                key={title}
                ref={ref}
                tabIndex={0} // Make focusable for keyboard users
                aria-label={`${title} capacity card`}
                className={`
                  relative group overflow-hidden rounded-xl shadow-lg 
                  focus:outline-teal-600 focus:outline-2 focus:outline-offset-2
                  transition-shadow duration-400 transform gpu-animation
                  ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-12'}
                  hover:shadow-2xl hover:scale-105 focus:scale-105 focus:shadow-2xl
                `}
                style={{ transitionProperty: 'opacity, transform, box-shadow, transform' }}
              >
                <div
                  className={`relative h-72 bg-gradient-to-br ${gradientFrom} ${gradientTo} rounded-xl`}
                >
                  {/* Background Image with lazy loading */}
                  <div
                    className="absolute inset-0 bg-cover bg-center filter brightness-90 transition-filter duration-500 group-hover:brightness-100"
                    style={{ backgroundImage: `url(${imageUrl})` }}
                    role="img"
                    aria-label={`${title} background image`}
                    loading="lazy"
                  />
                  {/* Overlay */}
                  <div
                    className={`absolute inset-0 ${overlayOpacity} ${hoverOverlayOpacity} transition-colors duration-500 rounded-xl`}
                  />
                  {/* Content */}
                  <div className="absolute inset-0 flex flex-col items-center justify-center text-center px-6">
                    <h3 className="text-4xl font-semibold text-white drop-shadow-md mb-4 tracking-wide shadow-black transition-colors group-hover:text-teal-300">
                      {title}
                    </h3>
                    <p className="text-white text-sm max-w-xs opacity-95 leading-relaxed drop-shadow-sm transition-opacity group-hover:opacity-100">
                      {description}
                    </p>
                  </div>
                </div>
                {/* Link overlay */}
                <Link
                  href={href}
                  className="absolute inset-0"
                  aria-label={`Learn more about ${title}`}
                  tabIndex={-1} // Prevent tab on link since entire card is focusable
                />
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default CapacitySection;
