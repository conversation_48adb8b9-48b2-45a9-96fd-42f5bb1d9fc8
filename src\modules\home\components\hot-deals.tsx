import { ChevronLeft, ChevronRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

const hotDeals = [
  { id: "NP15790-13", name: "LH MAIN WINDSHIELD", price: "$2,450" },
  { id: "69003810-101", name: "TOP ASSEMBLY", price: "$1,850" },
  { id: "20301573-102", name: "AIR SEPARATOR", price: "$3,200" },
  { id: "93058117", name: "ANTENNA", price: "$750" },
  { id: "066-500", name: "HYDRAULIC FILTER", price: "$425" },
];

const HotDeals = () => {
  return (
    <section className="bg-secondary/40 text-primary py-4">
      <div className="container mx-auto px-4">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <span className="font-bold text-lg">HOT DEALS:</span>
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm" className="text-accent-foreground hover:bg-white/20">
              <ChevronLeft className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="flex-1 overflow-hidden">
            <div className="flex gap-6 animate-scroll">
              {hotDeals.map((deal) => (
                <div key={deal.id} className="flex-shrink-0 flex items-center gap-4">
                  <span className="font-mono text-sm">{deal.id}</span>
                  <span className="text-sm">{deal.name}</span>
                  <span className="font-bold text-sm">{deal.price}</span>
                  <span className="text-white/60">|</span>
                </div>
              ))}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm" className="text-accent-foreground hover:bg-white/20">
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HotDeals;

// 'use client';

// import { useState, useEffect } from 'react';
// import { ChevronRight } from 'lucide-react';

// const HotDealsSlider = () => {
//   const [currentIndex, setCurrentIndex] = useState(0);

//   // Hot deals data exactly as shown in the image
//   const hotDeals = [
//     "9305WBIT / ANTENNA",
//     "CTS800-4N / GAS TURBINE", 
//     "142185-1115 / AHRU",
//     "3G6210A00131 / MAIN ROTOR BLADE",
//     "NP157901-13 / LH MAIN",
//     "H125-ENGINE-001 / ENGINE ASSEMBLY",
//     "A320-WING-502 / WING COMPONENT",
//     "ATR72-PROP-303 / PROPELLER BLADE"
//   ];

//   // Auto-slide functionality
//   useEffect(() => {
//     const interval = setInterval(() => {
//       setCurrentIndex((prevIndex) => 
//         prevIndex >= hotDeals.length - 1 ? 0 : prevIndex + 1
//       );
//     }, 4000); // Slide every 4 seconds

//     return () => clearInterval(interval);
//   }, [hotDeals.length]);

//   return (
//     <div className="bg-gradient-to-r from-teal-500 via-blue-500 to-blue-600 text-white py-3 overflow-hidden relative">
//       {/* Background image overlay to match the sky/aircraft theme */}
//       <div 
//         className="absolute inset-0 opacity-20 bg-cover bg-center"
//         style={{
//           backgroundImage: 'url("data:image/svg+xml,%3Csvg width="100" height="100" xmlns="http://www.w3.org/2000/svg"%3E%3Cdefs%3E%3Cpattern id="aircraft" x="0" y="0" width="50" height="50" patternUnits="userSpaceOnUse"%3E%3Cpath d="M10 25 L40 25 M25 10 L25 40" stroke="white" stroke-width="0.5" opacity="0.3"/%3E%3C/pattern%3E%3C/defs%3E%3Crect width="100" height="100" fill="url(%23aircraft)"/%3E%3C/svg%3E")'
//         }}
//       />
      
//       <div className="max-w-7xl mx-auto px-4 relative z-10">
//         <div className="flex items-center justify-between">
//           {/* Hot Deals Label */}
//           <div className="flex-shrink-0">
//             <span className="text-lg font-bold tracking-wide text-white">HOT DEALS:</span>
//           </div>

//           {/* Sliding Content */}
//           <div className="flex-1 mx-8 overflow-hidden">
//             <div 
//               className="flex transition-transform duration-1000 ease-in-out"
//               style={{ 
//                 transform: `translateX(-${currentIndex * (100 / hotDeals.length)}%)`,
//                 width: `${hotDeals.length * 100}%`
//               }}
//             >
//               {hotDeals.map((deal, index) => (
//                 <div
//                   key={index}
//                   className={`flex-shrink-0 text-center transition-all duration-500 ${
//                     index === currentIndex 
//                       ? 'opacity-100 scale-100' 
//                       : 'opacity-70 scale-95'
//                   }`}
//                   style={{ width: `${100 / hotDeals.length}%` }}
//                 >
//                   <span className="text-base font-semibold tracking-wider text-white hover:text-yellow-300 transition-colors cursor-pointer">
//                     {deal}
//                   </span>
//                 </div>
//               ))}
//             </div>
//           </div>

//           {/* Arrow Button */}
//           <div className="flex-shrink-0">
//             <button
//               onClick={() => setCurrentIndex((prev) => 
//                 prev >= hotDeals.length - 1 ? 0 : prev + 1
//               )}
//               className="p-2 bg-white/20 hover:bg-white/30 rounded-full transition-all duration-200 hover:scale-110"
//               aria-label="Next deal"
//             >
//               <ChevronRight className="w-5 h-5 text-white" />
//             </button>
//           </div>
//         </div>

//         {/* Progress indicator dots */}
//         <div className="flex justify-center mt-2 space-x-1">
//           {hotDeals.map((_, index) => (
//             <div
//               key={index}
//               className={`w-1.5 h-1.5 rounded-full transition-all duration-300 ${
//                 index === currentIndex 
//                   ? 'bg-yellow-300 scale-125' 
//                   : 'bg-white/40'
//               }`}
//             />
//           ))}
//         </div>
//       </div>

//       {/* Animated background elements */}
//       <div className="absolute inset-0 overflow-hidden pointer-events-none">
//         <div className="absolute top-1/2 -left-4 w-8 h-8 bg-white/10 rounded-full animate-pulse"></div>
//         <div className="absolute top-1/4 right-1/4 w-6 h-6 bg-white/5 rounded-full animate-pulse delay-1000"></div>
//         <div className="absolute bottom-1/3 left-1/3 w-4 h-4 bg-white/10 rounded-full animate-pulse delay-2000"></div>
//       </div>
//     </div>
//   );
// };

// export default HotDealsSlider;