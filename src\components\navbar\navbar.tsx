'use client';

import { useState } from 'react';
import Link from 'next/link';
import { ChevronDown, Search, Phone, Mail, MapPin, Menu, X } from 'lucide-react';
import Image from 'next/image';

interface NavItem {
  name: string;
  href: string;
}

interface NavCategory {
  category: string;
  items: NavItem[];
}

const Navbar = () => {
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [isMenuOpen, setIsMenuOpen] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>('');

  // Aircraft dropdown menu items
  const aircraftMenuItems: NavCategory[] = [
    {
      category: 'AIRLINES',
      items: [
        { name: 'AIRBUS A330-A340', href: '/aircraft/airbus-a330-a340' },
        { name: 'AIRBUS A320 FAMILY AND NEO', href: '/aircraft/airbus-a320' },
        { name: 'BOEING B737NG', href: '/aircraft/boeing-b737ng' },
        { name: 'BOEING B727', href: '/aircraft/boeing-b727' },
        { name: 'BOEING B767/B777', href: '/aircraft/boeing-b767-b777' },
        { name: 'EMBRAER E-JET FAMILY E190 AND E170', href: '/aircraft/embraer-ejet' },
        { name: 'BAE146/AVRO RJ', href: '/aircraft/bae146-avro' },
        { name: 'BAE125-700/800 (HAWKER)', href: '/aircraft/bae125-hawker' },
        { name: 'BAE JETSTREAM 31/32', href: '/aircraft/bae-jetstream' },
        { name: 'ATR 72', href: '/aircraft/atr-72' },
        { name: 'BOMBARDIER DASH 8', href: '/aircraft/bombardier-dash8' },
        { name: 'GULFSTREAM G-IV', href: '/aircraft/gulfstream-g4' },
        { name: 'BAES HAWK JET', href: '/aircraft/baes-hawk' },
        { name: 'SAAB 340 / SAAB 2000', href: '/aircraft/saab-340-2000' }
      ]
    },
    {
      category: 'HELICOPTERS',
      items: [
        { name: 'AGUSTA AW109A, AW109C, AW109E AND AW109S', href: '/aircraft/agusta-aw109' },
        { name: 'AGUSTA AW139', href: '/aircraft/agusta-aw139' },
        { name: 'AGUSTA AW189', href: '/aircraft/agusta-aw189' },
        { name: 'AGUSTA AW101 MERLIN', href: '/aircraft/agusta-aw101' },
        { name: 'AIRBUS HELICOPTERS SUPER PUMA', href: '/aircraft/airbus-super-puma' },
        { name: 'LYNX HELICOPTERS', href: '/aircraft/lynx-helicopters' },
        { name: 'SIKORSKY S61/SEAKING', href: '/aircraft/sikorsky-s61' },
        { name: 'SIKORSKY S76 HELICOPTER SPARES', href: '/aircraft/sikorsky-s76' },
        { name: 'SIKORSKY S92', href: '/aircraft/sikorsky-s92' },
        { name: 'AIRBUS HELICOPTERS AS350', href: '/aircraft/airbus-as350' }
      ]
    }
  ];

  // About Us dropdown items
  const aboutMenuItems: NavItem[] = [
    { name: 'OUR COMPANY', href: '/about/company' },
    { name: 'OUR TEAM', href: '/about/team' },
    { name: 'OUR MISSION', href: '/about/mission' },
    { name: 'CAREERS', href: '/about/careers' },
    { name: 'NEWS & MEDIA', href: '/about/news' }
  ];

  // Tailored System Support dropdown items
  const tailoredSupportItems: NavItem[] = [
    { name: 'AIRCRAFT COMPONENT REPAIR', href: '/tailored-support/repair' },
    { name: 'ENGINEERING SERVICES', href: '/tailored-support/engineering' },
    { name: 'TECHNICAL SUPPORT', href: '/tailored-support/technical' },
    { name: 'LOGISTICS SOLUTIONS', href: '/tailored-support/logistics' },
    { name: 'INVENTORY MANAGEMENT', href: '/tailored-support/inventory' }
  ];

  // Defence dropdown items
  const defenceItems: NavItem[] = [
    { name: 'MILITARY AIRCRAFT', href: '/defence/military-aircraft' },
    { name: 'HELICOPTER SYSTEMS', href: '/defence/helicopter-systems' },
    { name: 'AVIONICS', href: '/defence/avionics' },
    { name: 'GROUND SUPPORT EQUIPMENT', href: '/defence/ground-support' },
    { name: 'TRAINING SOLUTIONS', href: '/defence/training' }
  ];

  const handleMouseEnter = (menu: string) => {
    setActiveDropdown(menu);
  };

  const handleMouseLeave = () => {
    setActiveDropdown(null);
  };

  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      console.log('Search query:', searchQuery);
      // Implement search logic here
    }
  };

  // Helper function to render dropdown menu
  const renderDropdownMenu = (items: NavItem[] | NavCategory[], title: string, isCategory = false) => {
    return (
      <div className="absolute top-full left-0 bg-secondary shadow-lg z-50 w-[500px] p-6 border-t-2 border-teal-400">
        <h3 className="text-2xl font-bold mb-6">{title}</h3>
        {isCategory ? (
          <div className="grid grid-cols-2 gap-6">
            {(items as NavCategory[]).map((category) => (
              <div key={category.category}>
                <h4 className="text-lg font-medium mb-3">{category.category}</h4>
                <ul className="space-y-2 text-sm">
                  {category.items.map((item) => (
                    <li key={item.name}>
                      <Link
                        href={item.href}
                        className="hover:text-teal-200 cursor-pointer transition-colors block"
                      >
                        → {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        ) : (
          <ul className="space-y-3">
            {(items as NavItem[]).map((item) => (
              <li key={item.name}>
                <Link
                  href={item.href}
                  className="hover:text-teal-200 cursor-pointer transition-colors block text-sm"
                >
                  → {item.name}
                </Link>
              </li>
            ))}
          </ul>
        )}
      </div>
    );
  };

  return (
    <div className="bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-2 md:px-4 ">
          <div className="flex items-center justify-between py-4">
            {/* Logo and Company Name */}
            <Link href="/" className="">
              <Image
                src="/images/logo.png"
                alt="Mustang Airworks Logo"
                width={200}
                height={100}
                className="h-20 w-auto"
              />
            </Link>

            {/* Contact Info - Desktop */}
            <div className='space-y-2'>
              <div className="hidden lg:flex items-center space-x-6">
                <div className="flex items-center space-x-2 text-primary">
                  <Phone className="w-4 h-4" />
                  <span className="font-medium">+977-9801000016</span>
                </div>
                <div className="flex items-center space-x-2 text-primary">
                  <MapPin className="w-4 h-4" />
                  <span className="font-medium">Lazimpat, Kathmandu</span>
                </div>
              </div>

              <div className="pb-4 flex justify-end">
                <div className="flex items-center max-w-md mx-auto md:mx-0">
                  <form onSubmit={handleSearch} className="relative flex-1">
                    <input
                      type="text"
                      placeholder="SEARCH FOR PARTS OR PAGES"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pr-10 py-1 px-2 bg-gray-50 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                    />
                    <button
                      type="submit"
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-secondary"
                    >
                      <Search className="w-4 h-4" />
                    </button>
                  </form>
                </div>
              </div>
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="lg:hidden text-gray-700 hover:text-secondary transition-colors"
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>


          </div>

          {/* Search Section */}

        </div>

        {/* Navigation with Dropdowns */}
        <nav className="bg-secondary text-white relative">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-12">
              {/* Desktop Navigation */}
              <div className="hidden lg:flex space-x-8">
                <Link href="/" className="hover:text-teal-200 font-medium transition-colors">
                  HOME
                </Link>

                {/* Aircraft Dropdown */}
                <div
                  className="relative"
                  onMouseEnter={() => handleMouseEnter('aircraft')}
                  onMouseLeave={handleMouseLeave}
                >
                  <Link href="/aircraft" className="hover:text-teal-200 font-medium flex items-center transition-colors">
                    AIRCRAFT <ChevronDown className="ml-1 w-4 h-4" />
                  </Link>
                  {activeDropdown === 'aircraft' && renderDropdownMenu(aircraftMenuItems, 'AIRCRAFT', true)}
                </div>

                {/* About Us Dropdown */}
                <div
                  className="relative"
                  onMouseEnter={() => handleMouseEnter('about')}
                  onMouseLeave={handleMouseLeave}
                >
                  <Link href="/about" className="hover:text-teal-200 font-medium flex items-center transition-colors">
                    ABOUT US <ChevronDown className="ml-1 w-4 h-4" />
                  </Link>
                  {activeDropdown === 'about' && renderDropdownMenu(aboutMenuItems, 'ABOUT US')}
                </div>

                {/* Tailored Support Dropdown */}
                <div
                  className="relative"
                  onMouseEnter={() => handleMouseEnter('tailored')}
                  onMouseLeave={handleMouseLeave}
                >
                  <Link href="/tailored-support" className="hover:text-teal-200 font-medium flex items-center transition-colors">
                    TAILORED SYSTEM SUPPORT <ChevronDown className="ml-1 w-4 h-4" />
                  </Link>
                  {activeDropdown === 'tailored' && renderDropdownMenu(tailoredSupportItems, 'TAILORED SYSTEM SUPPORT')}
                </div>

                {/* Defence Dropdown */}
                <div
                  className="relative"
                  onMouseEnter={() => handleMouseEnter('defence')}
                  onMouseLeave={handleMouseLeave}
                >
                  <Link href="/defence" className="hover:text-teal-200 font-medium flex items-center transition-colors">
                    DEFENCE <ChevronDown className="ml-1 w-4 h-4" />
                  </Link>
                  {activeDropdown === 'defence' && renderDropdownMenu(defenceItems, 'DEFENCE')}
                </div>

                <Link href="/certified" className="hover:text-teal-200 font-medium transition-colors">
                  CERTIFIED
                </Link>

                <Link href="/contact" className="hover:text-teal-200 font-medium transition-colors">
                  CONTACT US
                </Link>
              </div>

              {/* LINE CARD Button */}
              <div className="hidden lg:block">
                <Link
                  href="/line-card"
                  className="bg-red-500 hover:bg-red-600 px-4 py-1 rounded text-xs font-semibold transition-colors"
                >
                  LINE CARD 2025
                </Link>
              </div>

              {/* Mobile Menu Toggle Text */}
              <div className="lg:hidden text-sm font-medium">
                {isMenuOpen ? 'Close Menu' : 'Menu'}
              </div>
            </div>
          </div>

          {/* Mobile Menu */}
          {isMenuOpen && (
            <div className="lg:hidden bg-teal-700 border-t border-teal-500">
              <div className="px-4 py-4 space-y-4">
                {/* Mobile Contact Info */}
                <div className="space-y-2 text-sm border-b border-teal-500 pb-4">
                  <div className="flex items-center space-x-2">
                    <Phone className="w-3 h-3" />
                    <span>+977-1-4169802</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Phone className="w-3 h-3" />
                    <span>+977-9801000016</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Mail className="w-3 h-3" />
                    <span><EMAIL></span>
                  </div>
                </div>

                {/* Mobile Navigation Links */}
                <Link
                  href="/"
                  className="block hover:text-teal-200 font-medium py-2 transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  HOME
                </Link>

                {/* Mobile Aircraft Menu */}
                <div>
                  <button
                    onClick={() => handleMouseEnter(activeDropdown === 'mobile-aircraft' ? '' : 'mobile-aircraft')}
                    className="flex items-center justify-between w-full hover:text-teal-200 font-medium py-2 transition-colors"
                  >
                    <span>AIRCRAFT</span>
                    <ChevronDown
                      className={`w-4 h-4 transform transition-transform ${activeDropdown === 'mobile-aircraft' ? 'rotate-180' : ''
                        }`}
                    />
                  </button>

                  {activeDropdown === 'mobile-aircraft' && (
                    <div className="pl-4 mt-2 space-y-3">
                      {aircraftMenuItems.map((category) => (
                        <div key={category.category}>
                          <h4 className="font-semibold text-teal-200 mb-2 text-sm">
                            {category.category}
                          </h4>
                          <div className="space-y-1">
                            {category.items.map((item) => (
                              <Link
                                key={item.name}
                                href={item.href}
                                className="block text-xs text-teal-100 hover:text-white py-1 pl-2 transition-colors"
                                onClick={() => {
                                  setIsMenuOpen(false);
                                  setActiveDropdown(null);
                                }}
                              >
                                → {item.name}
                              </Link>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Mobile About Us Menu */}
                <div>
                  <button
                    onClick={() => handleMouseEnter(activeDropdown === 'mobile-about' ? '' : 'mobile-about')}
                    className="flex items-center justify-between w-full hover:text-teal-200 font-medium py-2 transition-colors"
                  >
                    <span>ABOUT US</span>
                    <ChevronDown
                      className={`w-4 h-4 transform transition-transform ${activeDropdown === 'mobile-about' ? 'rotate-180' : ''
                        }`}
                    />
                  </button>

                  {activeDropdown === 'mobile-about' && (
                    <div className="pl-4 mt-2 space-y-2">
                      {aboutMenuItems.map((item) => (
                        <Link
                          key={item.name}
                          href={item.href}
                          className="block text-xs text-teal-100 hover:text-white py-1 pl-2 transition-colors"
                          onClick={() => {
                            setIsMenuOpen(false);
                            setActiveDropdown(null);
                          }}
                        >
                          → {item.name}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>

                {/* Mobile Tailored Support Menu */}
                <div>
                  <button
                    onClick={() => handleMouseEnter(activeDropdown === 'mobile-tailored' ? '' : 'mobile-tailored')}
                    className="flex items-center justify-between w-full hover:text-teal-200 font-medium py-2 transition-colors"
                  >
                    <span>TAILORED SYSTEM SUPPORT</span>
                    <ChevronDown
                      className={`w-4 h-4 transform transition-transform ${activeDropdown === 'mobile-tailored' ? 'rotate-180' : ''
                        }`}
                    />
                  </button>

                  {activeDropdown === 'mobile-tailored' && (
                    <div className="pl-4 mt-2 space-y-2">
                      {tailoredSupportItems.map((item) => (
                        <Link
                          key={item.name}
                          href={item.href}
                          className="block text-xs text-teal-100 hover:text-white py-1 pl-2 transition-colors"
                          onClick={() => {
                            setIsMenuOpen(false);
                            setActiveDropdown(null);
                          }}
                        >
                          → {item.name}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>

                {/* Mobile Defence Menu */}
                <div>
                  <button
                    onClick={() => handleMouseEnter(activeDropdown === 'mobile-defence' ? '' : 'mobile-defence')}
                    className="flex items-center justify-between w-full hover:text-teal-200 font-medium py-2 transition-colors"
                  >
                    <span>DEFENCE</span>
                    <ChevronDown
                      className={`w-4 h-4 transform transition-transform ${activeDropdown === 'mobile-defence' ? 'rotate-180' : ''
                        }`}
                    />
                  </button>

                  {activeDropdown === 'mobile-defence' && (
                    <div className="pl-4 mt-2 space-y-2">
                      {defenceItems.map((item) => (
                        <Link
                          key={item.name}
                          href={item.href}
                          className="block text-xs text-teal-100 hover:text-white py-1 pl-2 transition-colors"
                          onClick={() => {
                            setIsMenuOpen(false);
                            setActiveDropdown(null);
                          }}
                        >
                          → {item.name}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>

                <Link
                  href="/certified"
                  className="block hover:text-teal-200 font-medium py-2 transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  CERTIFIED
                </Link>

                <Link
                  href="/contact"
                  className="block hover:text-teal-200 font-medium py-2 transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  CONTACT US
                </Link>

                {/* Mobile LINE CARD Button */}
                <Link
                  href="/line-card"
                  className="block bg-red-500 hover:bg-red-600 px-4 py-2 rounded text-center text-sm font-semibold transition-colors mt-4"
                  onClick={() => setIsMenuOpen(false)}
                >
                  LINE CARD 2025
                </Link>
              </div>
            </div>
          )}
        </nav>
      </header>
    </div>
  );
};

export default Navbar;