import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Search } from 'lucide-react'
import React from 'react'

const HeroSection = () => {
    return (
        <div>
            <section className="relative h-[500px] bg-gradient-to-r from-gray-600 to-gray-800 flex items-center bg-cover bg-center" style={{backgroundImage: `url("/images/hero.jpg")`}}>
                <div className="absolute inset-0 bg-black opacity-40"></div>
                <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
                    <div className="text-white">
                        <h2 className="text-5xl font-bold mb-4">INSTANT<br />PART SEARCH</h2>
                        <div className="max-w-md mb-6">
                            <div className="flex">
                                <Input
                                    placeholder="SEARCH BY PART NUMBER / USE * AS A WILDCARD"
                                    className="bg-white text-black flex-1"
                                />
                                <Button className="bg-primary hover:bg-secondary ml-2">
                                    <Search className="w-4 h-4" />
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    )
}

export default HeroSection